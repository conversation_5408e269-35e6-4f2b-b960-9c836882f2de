package com.jidou.cariad.content.common.utils;

import com.jidou.cariad.content.common.interceptor.RequestIdInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class TraceIdUtils {

    public static final String TRACE_ID = "traceId";
    public static final String P_VIN = "pVin";
    public static final String STATUS = "status";

    public static final String BRAND = "brand";
    public static final String URL = "url";

    public static final String AN_INTERFACE = "anInterface";

    public static final String DEVICE_ID = "deviceId";

    /**
     * 生成 traceId
     */
    private static String genTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 设置车辆VIN码
     * 自动添加VIN:前缀
     *
     * @param pVin 车辆VIN码
     */
    public static void setPVin(String pVin) {
        if (StringUtils.isNotBlank(pVin)) {
            String formattedPVin = pVin.startsWith("VIN:") ? pVin : "VIN:" + pVin;
            MDC.put(P_VIN, formattedPVin);
        }
    }

    /**
     * 设置品牌
     * 自动添加BRAND:前缀
     *
     * @param brand 品牌
     */
    public static void setBrand(String brand) {
        if (StringUtils.isNotBlank(brand)) {
            String formattedBrand = brand.startsWith("BRAND:") ? brand : "BRAND:" + brand;
            MDC.put(BRAND, formattedBrand);
        }
    }

    /**
     * 设置URL
     *
     * @param url URL地址
     */
    public static void setUrl(String url) {
        if (StringUtils.isNotBlank(url)) {
            MDC.put(URL, url);
        }
    }

    /**
     * 设置状态
     * 自动添加STATUS:前缀
     *
     * @param status 状态
     */
    public static void setStatus(String status) {
        if (StringUtils.isNotBlank(status)) {
            String formattedStatus = status.startsWith("STATUS:") ? status : "STATUS:" + status;
            MDC.put(STATUS, formattedStatus);
        }
    }

    /**
     * 设置接口名称
     * 自动添加API:前缀
     *
     * @param anInterface 接口名称
     */
    public static void setAnInterface(String anInterface) {
        if (StringUtils.isNotBlank(anInterface)) {
            String formattedInterface = anInterface.startsWith("API:") ? anInterface : "API:" + anInterface;
            MDC.put(AN_INTERFACE, formattedInterface);
        }
    }

    /**
     * 设置设备ID
     * 如果deviceId不为空且不包含前缀，则自动添加DID:前缀
     *
     * @param deviceId 设备ID
     */
    public static void setDeviceId(String deviceId) {
        if (StringUtils.isNotBlank(deviceId)) {
            // 如果deviceId不包含前缀，则添加deviceID:前缀
            String formattedDeviceId = deviceId.startsWith("deviceId:") ? deviceId : "deviceId:" + deviceId;
            MDC.put(DEVICE_ID, formattedDeviceId);
        }
    }

    /**
     * 获取 traceId
     */
    public static String getTraceId() {
        // 获取
        String traceId = MDC.get(TRACE_ID);
        // 如果 traceId 为空，则尝试从requestId获取
        if (StringUtils.isBlank(traceId)) {
            traceId = MDC.get(RequestIdInterceptor.REQUEST_ID);
        }
        // 如果仍然为空，则生成新 ID
        return StringUtils.isBlank(traceId) ? genTraceId() : traceId;
    }

    /**
     * 设置 traceId
     */
    public static void setTraceId(String traceId) {
        // 如果参数为空，则生成新 ID
        traceId = StringUtils.isBlank(traceId) ? genTraceId() : traceId;
        // 将 traceId 放到 MDC 中
        MDC.put(TRACE_ID, traceId);

        // 同步设置requestId，保持一致性
        if (StringUtils.isBlank(MDC.get(RequestIdInterceptor.REQUEST_ID))) {
            MDC.put(RequestIdInterceptor.REQUEST_ID, traceId);
        }
    }

    /**
     * 获取 requestId
     */
    public static String getRequestId() {
        // 获取requestId
        String requestId = MDC.get(RequestIdInterceptor.REQUEST_ID);
        // 如果requestId为空，则尝试从traceId获取
        if (StringUtils.isBlank(requestId)) {
            requestId = MDC.get(TRACE_ID);
        }
        // 如果仍然为空，则生成新 ID
        return StringUtils.isBlank(requestId) ? genTraceId() : requestId;
    }

    /**
     * 移除 traceId
     */
    public static void removeTraceId() {
        MDC.remove(TRACE_ID);
        MDC.remove(RequestIdInterceptor.REQUEST_ID);
    }


    /**
     * remove all
     */
    public static void removeAll() {
        MDC.remove(TRACE_ID);
        MDC.remove(P_VIN);
        MDC.remove(STATUS);
        MDC.remove(BRAND);
        MDC.remove(URL);
        MDC.remove(AN_INTERFACE);
        MDC.remove(DEVICE_ID);
        MDC.remove(RequestIdInterceptor.REQUEST_ID);
    }
}

