package com.jidou.cariad.content.common.interceptor;

import com.jidou.cariad.content.common.utils.TraceIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * 请求ID拦截器
 * 为每个请求生成唯一的requestId和deviceId，并放入MDC中
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class RequestIdInterceptor implements HandlerInterceptor {

    public static final String REQUEST_ID = "requestId";
    public static final String REQUEST_ID_HEADER = "X-Request-Id";
    public static final String DEVICE_ID_HEADER = "deviceId";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 处理requestId
        handleRequestId(request, response);

        // 处理deviceId
        handleDeviceId(request);

        return true;
    }

    /**
     * 处理请求ID
     * 优先从请求头中获取requestId，如果没有则生成新的
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     */
    private void handleRequestId(HttpServletRequest request, HttpServletResponse response) {
        // 优先从请求头中获取requestId
        String requestId = request.getHeader(REQUEST_ID_HEADER);

        // 如果请求头中没有requestId，则生成一个新的
        if (StringUtils.isBlank(requestId)) {
            requestId = generateRequestId();
        }

        // 将requestId放入MDC中
        MDC.put(REQUEST_ID, requestId);

        // 将requestId放入响应头中
        response.setHeader(REQUEST_ID_HEADER, requestId);

        // 同时设置traceId，保持兼容性
        TraceIdUtils.setTraceId(requestId);
    }

    /**
     * 处理设备ID
     * 从请求头中获取deviceId并放入MDC中
     *
     * @param request HTTP请求
     */
    private void handleDeviceId(HttpServletRequest request) {
        String deviceId = request.getHeader(DEVICE_ID_HEADER);

        // 如果deviceId不为空，则放入MDC中（TraceIdUtils会自动添加前缀）
        if (StringUtils.isNotBlank(deviceId)) {
            TraceIdUtils.setDeviceId(deviceId);
            log.debug("设置deviceId到MDC: {}", deviceId);
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
        // 清理MDC中的requestId
        MDC.remove(REQUEST_ID);
        // 清理其他相关信息
        TraceIdUtils.removeAll();
    }

    /**
     * 生成请求ID
     *
     * @return 32位无连字符的UUID字符串
     */
    private String generateRequestId() {
        return "requestId:" + UUID.randomUUID().toString().replace("-", "");
    }
}
