package com.jidou.cariad.content.wind.cache;

import com.jidou.cariad.content.model.vo.weather.WeatherHourlyVO;
import com.jidou.cariad.content.wind.dto.WindCacheData;

import java.util.List;

/**
 * 风速缓存服务接口
 * 专门处理风速数据的缓存、更新和获取逻辑
 *
 * <AUTHOR>
 * @since 2024/12/19
 */
public interface WindCacheService {

    /**
     * 为逐小时天气数据设置风速信息
     * 优先从缓存获取，必要时触发同步更新
     *
     * @param hourly 逐小时天气数据列表
     */
    void setHourlyWindSpeed(List<WeatherHourlyVO> hourly);

    /**
     * 获取风速缓存数据
     *
     * @param locationPath 地区路径
     * @return 风速缓存数据，不存在时返回null
     */
    WindCacheData getWindCacheData(String locationPath);

    /**
     * 为计划内城市更新风速数据到缓存
     *
     * <p>供定时任务使用，同步执行。特点：
     * <ul>
     *   <li>缓存时间：6天（计划内城市长期缓存）</li>
     *   <li>延迟：无限制（后台任务可等待）</li>
     *   <li>锁定时间：5分钟</li>
     *   <li>数据源标记：nmc</li>
     *   <li>支持队列机制：限流时加入队列等待处理</li>
     * </ul>
     *
     * @param locationPath 地区路径，如 "beijing/beijing"
     * @param ignoreCache 是否忽略缓存，true-忽略缓存，false-使用缓存
     * @since 2.0
     */
    void updateWindSpeedDataForScheduledCity(String locationPath, Boolean ignoreCache);

    /**
     * 判断缓存数据是否需要更新
     *
     * @param cacheData 缓存数据
     * @return true-需要更新，false-不需要更新
     */
    boolean shouldUpdateCache(WindCacheData cacheData);

    /**
     * 处理队列中的风速数据请求
     * 供定时任务使用，同步执行
     */
    void processQueuedRequests();

    /**
     * 将请求加入Redis队列，等待后续处理
     *
     * @param locationPath 地区路径
     */
    void addToRequestQueue(String locationPath);

    /**
     * 清理过期的城市锁
     * 这是一个安全措施，防止锁泄漏导致城市永久被锁定
     */
    void cleanupExpiredLocks();

    /**
     * 强制释放指定城市的锁
     * 用于故障排查和恢复
     *
     * @param locationPath 地区路径
     */
    void forceUnlockCity(String locationPath);
}
